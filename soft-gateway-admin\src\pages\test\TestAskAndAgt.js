import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';

const TestAskAndAgt = () => {
  
  //实现一个小功能，上传一个图片，去除图片的水印并显示去除水印后的图片
  const test1 = () => {
    
  };

  //用户登录，基于用户名和密码实现登录；未输入需要提示错误；
  //人员管理，人员信息包括用户名、密码、姓名、性别、年龄、电话号码、邮箱地址、状态和注册时间，请实现人员列表、人员查询、人员添加、人员编辑和人员删除功能；密码需要加密存储；
  //前端使用antd实现，界面使用绿色风格；后端使用springboot实现，数据库可以使用内置的derby数据库。
  const test2 = () => {
    
  };
  

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>测试问题和智能体</h2>
      </div>      
    </div>
  );
};

export default TestAskAndAgt;