import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';

const TestProblem = () => {
  const [state, setState] = useState({});
  // 1、PlusOutlined的引用错误
  //2、递归错误
  const pow = (x, n) => {
    return x * pow(x, n - 1);
  };
  //3、类型错误
  const testType = () => {
    let a = 100;
    console.log(a.toString());
    let x;
    console.log(x.toString());
  };
  //4、范围错误
  const testRange = () => {
    let a = [1];
    console.log(a[2]);
  };
  //5、对未声明变量赋值
  const testVal = () => {
    str = "aaa";
    console.log(str);
  };
  //6、语法错误
  const testYfErrot = (x) => {
    if (x > 5 { // 缺少右括号
      console.log('x is greater than 5');
    }
  };

  useEffect(() => {
    pow(10, 5);
    testType();
    return () => {//返回清除函数

    };
    // eslint-disable-next-line
  }, []);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>测试解决问题和BUG</h2>
        <div><PlusOutlined /></div>
      </div>
    </div>
  );
};

export default TestProblem;