import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';
import { PlusOutlined } from '@ant-design/icons';
import { useEffect } from 'react';

const TestProblem = () => {
  const [state, setState] = useState({});
  
  // 1、PlusOutlined的引用错误 - 已修复：添加PlusOutlined导入
  
  //2、递归错误 - 添加终止条件
  const pow = (x, n) => {
    if (n === 0) return 1;
    if (n === 1) return x;
    return x * pow(x, n - 1);
  };
  
  //3、类型错误 - 添加空值检查
  const testType = () => {
    let a = 100;
    console.log(a.toString());
    let x;
    if (x !== null && x !== undefined) {
      console.log(x.toString());
    }
  };
  
  //4、范围错误 - 添加边界检查
  const testRange = () => {
    let a = [1];
    if (a.length > 2) {
      console.log(a[2]);
    }
  };
  
  //5、对未声明变量赋值 - 添加变量声明
  const testVal = () => {
    let str = "aaa";
    console.log(str);
  };
  
  //6、语法错误 - 修复括号
  const testYfError = (x) => {
    if (x > 5) {
      console.log('x is greater than 5');
    }
  };

  useEffect(() => {
    try {
      pow(10, 5);
      testType();
    } catch (error) {
      console.error('Error in useEffect:', error);
    }
    return () => {
      // 清理函数
    };
  }, []);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>测试解决问题和BUG</h2>
        <div><PlusOutlined /></div>
      </div>
    </div>
  );
};

export default TestProblem;