import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import WatermarkRemover from './pages/watermark-remover/WatermarkRemover'
import TestProblem from './pages/test/TestProblem'
import TestAskAndAgt from './pages/test/TestAskAndAgt'
import TestGenerate from './pages/test/TestGenerate'
import TestCompletion from './pages/test/TestCompletion'
import TestLineCommd from './pages/test/TestLineCommd'

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <BrowserRouter>
        <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
          <Routes>
            <Route path="/" element={<Navigate to="/watermark-remover" replace />} />
            <Route path="/watermark-remover" element={<WatermarkRemover />} />
            <Route path="/test/problem" element={<TestProblem />} />
            <Route path="/test/ask-agt" element={<TestAskAndAgt />} />
            <Route path="/test/generate" element={<TestGenerate />} />
            <Route path="/test/completion" element={<TestCompletion />} />
            <Route path="/test/line-commd" element={<TestLineCommd />} />
          </Routes>
        </div>
      </BrowserRouter>
    </ConfigProvider>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
