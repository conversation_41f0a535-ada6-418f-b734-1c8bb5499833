import React, { useState, useRef } from 'react';
import { Upload, But<PERSON>, Card, Row, Col, message, Spin, Slider, Space, Menu } from 'antd';
import { UploadOutlined, DownloadOutlined, ClearOutlined, HomeOutlined, BugOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { UploadFile, UploadProps } from 'antd';

interface ProcessingOptions {
  threshold: number;
  blurRadius: number;
  iterations: number;
}

const WatermarkRemover: React.FC = () => {
  const navigate = useNavigate();
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [options, setOptions] = useState<ProcessingOptions>({
    threshold: 200,
    blurRadius: 2,
    iterations: 1
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 导航菜单项
  const menuItems = [
    {
      key: '/watermark-remover',
      icon: <HomeOutlined />,
      label: '水印去除',
    },
    {
      key: '/test/problem',
      icon: <BugOutlined />,
      label: '测试问题',
    },
    {
      key: '/test/ask-agt',
      icon: <BugOutlined />,
      label: '测试智能体',
    },
    {
      key: '/test/generate',
      icon: <BugOutlined />,
      label: '代码生成',
    },
    {
      key: '/test/completion',
      icon: <BugOutlined />,
      label: '代码补齐',
    },
    {
      key: '/test/line-commd',
      icon: <BugOutlined />,
      label: '命令行',
    },
  ];

  // 简单的水印去除算法
  const removeWatermark = (imageData: ImageData, options: ProcessingOptions): ImageData => {
    const { data, width, height } = imageData;
    const newData = new Uint8ClampedArray(data);
    
    // 基于阈值的水印检测和去除
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const alpha = data[i + 3];
      
      // 计算像素亮度
      const brightness = (r + g + b) / 3;
      
      // 如果像素亮度超过阈值，认为可能是水印
      if (brightness > options.threshold) {
        // 使用周围像素的平均值替换
        const x = (i / 4) % width;
        const y = Math.floor((i / 4) / width);
        
        let avgR = 0, avgG = 0, avgB = 0, count = 0;
        
        // 采样周围像素
        for (let dy = -options.blurRadius; dy <= options.blurRadius; dy++) {
          for (let dx = -options.blurRadius; dx <= options.blurRadius; dx++) {
            const nx = x + dx;
            const ny = y + dy;
            
            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
              const idx = (ny * width + nx) * 4;
              const neighborBrightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
              
              // 只使用非水印像素进行平均
              if (neighborBrightness <= options.threshold) {
                avgR += data[idx];
                avgG += data[idx + 1];
                avgB += data[idx + 2];
                count++;
              }
            }
          }
        }
        
        if (count > 0) {
          newData[i] = avgR / count;
          newData[i + 1] = avgG / count;
          newData[i + 2] = avgB / count;
        }
      }
    }
    
    return new ImageData(newData, width, height);
  };

  // 处理图片
  const processImage = async () => {
    if (!originalImage || !canvasRef.current) return;
    
    setLoading(true);
    
    try {
      const img = new Image();
      img.onload = () => {
        const canvas = canvasRef.current!;
        const ctx = canvas.getContext('2d')!;
        
        canvas.width = img.width;
        canvas.height = img.height;
        
        // 绘制原始图片
        ctx.drawImage(img, 0, 0);
        
        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        // 应用水印去除算法
        let processedData = imageData;
        for (let i = 0; i < options.iterations; i++) {
          processedData = removeWatermark(processedData, options);
        }
        
        // 将处理后的数据绘制到画布
        ctx.putImageData(processedData, 0, 0);
        
        // 转换为base64
        const processedDataUrl = canvas.toDataURL('image/png');
        setProcessedImage(processedDataUrl);
        setLoading(false);
        message.success('水印去除完成！');
      };
      
      img.src = originalImage;
    } catch (error) {
      console.error('处理图片时出错:', error);
      message.error('处理图片失败');
      setLoading(false);
    }
  };

  // 上传配置
  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件！');
        return false;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        setProcessedImage(null);
      };
      reader.readAsDataURL(file);
      
      return false; // 阻止自动上传
    },
    fileList,
    onChange: ({ fileList }) => setFileList(fileList),
    maxCount: 1,
  };

  // 下载处理后的图片
  const downloadImage = () => {
    if (!processedImage) return;
    
    const link = document.createElement('a');
    link.href = processedImage;
    link.download = 'watermark-removed.png';
    link.click();
  };

  // 清除所有内容
  const clearAll = () => {
    setOriginalImage(null);
    setProcessedImage(null);
    setFileList([]);
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      {/* 顶部导航 */}
      <div style={{ backgroundColor: '#fff', marginBottom: '24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <Menu
          mode="horizontal"
          selectedKeys={['/watermark-remover']}
          items={menuItems}
          onClick={({ key }) => navigate(key)}
          style={{ borderBottom: 'none' }}
        />
      </div>

      <div style={{ padding: '24px' }}>
        <Card title="图片水印去除工具" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 上传区域 */}
          <Card size="small" title="上传图片">
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>选择图片</Button>
            </Upload>
          </Card>

          {/* 参数调整 */}
          {originalImage && (
            <Card size="small" title="处理参数">
              <Row gutter={16}>
                <Col span={8}>
                  <div>亮度阈值: {options.threshold}</div>
                  <Slider
                    min={100}
                    max={255}
                    value={options.threshold}
                    onChange={(value) => setOptions(prev => ({ ...prev, threshold: value }))}
                  />
                </Col>
                <Col span={8}>
                  <div>模糊半径: {options.blurRadius}</div>
                  <Slider
                    min={1}
                    max={5}
                    value={options.blurRadius}
                    onChange={(value) => setOptions(prev => ({ ...prev, blurRadius: value }))}
                  />
                </Col>
                <Col span={8}>
                  <div>处理次数: {options.iterations}</div>
                  <Slider
                    min={1}
                    max={3}
                    value={options.iterations}
                    onChange={(value) => setOptions(prev => ({ ...prev, iterations: value }))}
                  />
                </Col>
              </Row>
            </Card>
          )}

          {/* 操作按钮 */}
          {originalImage && (
            <Space>
              <Button 
                type="primary" 
                onClick={processImage} 
                loading={loading}
                disabled={!originalImage}
              >
                去除水印
              </Button>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={downloadImage}
                disabled={!processedImage}
              >
                下载结果
              </Button>
              <Button 
                icon={<ClearOutlined />} 
                onClick={clearAll}
              >
                清除所有
              </Button>
            </Space>
          )}
        </Space>
      </Card>

      {/* 图片显示区域 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="原始图片" size="small">
            {originalImage ? (
              <img 
                src={originalImage} 
                alt="原始图片" 
                style={{ width: '100%', maxHeight: '400px', objectFit: 'contain' }}
              />
            ) : (
              <div style={{ 
                height: '200px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                color: '#999'
              }}>
                请上传图片
              </div>
            )}
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="处理结果" size="small">
            {loading ? (
              <div style={{ 
                height: '200px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center' 
              }}>
                <Spin size="large" />
              </div>
            ) : processedImage ? (
              <img 
                src={processedImage} 
                alt="处理后图片" 
                style={{ width: '100%', maxHeight: '400px', objectFit: 'contain' }}
              />
            ) : (
              <div style={{ 
                height: '200px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                color: '#999'
              }}>
                {originalImage ? '点击"去除水印"开始处理' : '等待上传图片'}
              </div>
            )}
          </Card>
        </Col>
      </Row>

        {/* 隐藏的canvas用于图像处理 */}
        <canvas ref={canvasRef} style={{ display: 'none' }} />
      </div>
    </div>
  );
};

export default WatermarkRemover;
