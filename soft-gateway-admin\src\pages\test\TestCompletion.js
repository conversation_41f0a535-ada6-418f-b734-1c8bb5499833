import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';

const TestCompletion = () => {
  const [form] = Form.useForm();

  const [sortType, setSortType] = useState([1, 2, 3]);

  const [gateways, setGateways] = useState([
    {
      id: 1,
      deviceCode: 'GW001',
      deviceId: 'gateway_001'
    },
    {
      id: 2,
      deviceCode: 'GW002',
      deviceId: 'gateway_002'
    },
  ]);

  const testSwitch = (status) => {//status的值可以是1，2，3
    let result = ""
    switch (status) {
      case 1:
        result = "启用";
        break;
      case 2:
        result = "禁用";
        break;
      case 3:
        result = "删除";
        break;
      default:
        result = "未知";
        break;
    }
    return result;
  };

  const testFor = () => {
    let plist = [];
    for (let i = 0; i < sortType.length; i++) {
      plist.push(testSwitch(sortType[i]));
    }
    return plist;
  };

  const testForEach = () => {
    gateways.forEach((item) => {
      console.log(item.deviceCode);
    });
  };
  
  const testTry = () => {
    try {
      // 你的代码
    } catch (error) {
      console.error(error);
    }
  };

  const testForm = () => {
     form.setFieldsValue({
      name: '测试组织',
      description: '这是一段测试描述',
    });
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>测试代码补齐</h2>
        <Form form={form} layout="vertical">
          <Form.Item
            label="组织名称"
            name="name"
            rules={[{ required: true, message: '请输入组织名称' }]}
          >
            <Input placeholder="请输入组织名称" />
          </Form.Item>
          <Form.Item
            label="组织描述"
            name="description"
          >
            <Input.TextArea placeholder="请输入组织描述" rows={4} />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default TestCompletion;