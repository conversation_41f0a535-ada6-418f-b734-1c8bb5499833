# 软网关管理系统

一个基于 React + Ant Design 的现代化管理系统，用于管理软网关设备和相关资源。

## 功能特性

### 🔐 用户认证
- 登录页面，支持用户名、密码和验证码验证
- 安全的用户会话管理

### 👤 个人中心
- **基本信息**：展示用户个人信息，包括用户名、姓名、性别、手机号码、电子邮件、账号状态、注册时间等
- **安全设置**：支持修改密码功能，包含密码强度验证

### 🛠️ 系统管理
- **组织机构管理**：支持树形结构的组织机构管理，包括添加、编辑、删除功能
- **用户管理**：完整的用户CRUD操作，支持查询、添加、编辑、删除、分配角色、重置密码、启用/停用用户
- **角色管理**：角色的增删改查，支持权限授权功能

### 🌐 软网关管理
- **软网关列表**：管理软网关设备，支持查询、添加、编辑、详情查看、导出、删除功能
- **原设备白名单**：管理设备白名单，支持查询、添加、编辑、删除和批量预分配功能

## 技术栈

- **前端框架**：React 18 + TypeScript
- **UI组件库**：Ant Design 5.22.5
- **构建工具**：Vite
- **路由管理**：React Router DOM
- **状态管理**：React Hooks
- **样式方案**：CSS + Ant Design 主题定制
- **日期处理**：Day.js

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 登录信息

系统提供了以下测试账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 拥有所有权限 |
| user1 | 123456 | 普通用户 | 技术部用户 |
| user2 | 123456 | 普通用户 | 运营部用户 |

## 项目结构

```
src/
├── components/          # 公共组件
│   └── Layout/         # 主布局组件
├── pages/              # 页面组件
│   ├── Login/          # 登录页面
│   ├── Dashboard/      # 首页仪表板
│   ├── Profile/        # 个人信息
│   ├── Security/       # 安全设置
│   ├── Organizations/  # 组织机构管理
│   ├── Users/          # 用户管理
│   ├── Roles/          # 角色管理
│   ├── Gateways/       # 软网关管理
│   └── DeviceWhitelist/ # 设备白名单管理
├── router/             # 路由配置
├── types/              # TypeScript 类型定义
├── utils/              # 工具函数
├── mock/               # 模拟数据
└── App.tsx             # 应用入口组件
```
