import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';

const TestGenerate = () => {
  
  //颜色的hex格式转换rgba格式
  const hexToRgba = (hex, opacity) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);  
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  };

  //把毫秒转成多少小时多少分钟多少秒多少毫秒
  const formatTime = (time) => {  
    const hours = Math.floor(time / 3600000);
    const minutes = Math.floor((time % 3600000) / 60000);
    const seconds = Math.floor((time % 60000) / 1000);  
    const milliseconds = time % 1000;  
    return `${hours}小时${minutes}分钟${seconds}秒${milliseconds}毫秒`;  
  };

  //把一个数字保留2位小数
  const formatNumber = (number) => {
    return number.toFixed(2);
  };

  //判定一个值是否为空
  const isEmpty = (value) => {
    if (value === null || value === undefined || value === '') {
      return true;
    } else {
      return false;
    }
  };

  //实现缓存数据并获取缓存数据
  const cacheData = (key, value) => {
    localStorage.setItem(key, value);
  };
  const getCacheData = (key) => {
    return localStorage.getItem(key);
  };

  //获得现在之前一天的时间
  const getYesterday = () => {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    return yesterday;
  };
  //把字符串数组，按照首字符升序排列
  const sortStringArray = (array) => {
    return array.sort();
  };
  
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>测试代码生成</h2>
      </div>      
    </div>
  );
};

export default TestGenerate;