# 图片水印去除工具

这是一个基于 React + Canvas API 实现的图片水印去除工具，可以帮助用户去除图片中的简单水印。

## 功能特性

### 🖼️ 图片处理
- **图片上传**：支持常见图片格式（JPG、PNG、GIF等）
- **实时预览**：上传后立即显示原始图片
- **参数调节**：可调整处理参数以获得最佳效果
- **结果对比**：左右对比显示原始图片和处理结果
- **下载功能**：支持下载处理后的图片

### ⚙️ 处理参数
- **亮度阈值**（100-255）：控制水印检测的敏感度，值越高检测越严格
- **模糊半径**（1-5）：控制周围像素采样范围，值越大处理效果越平滑
- **处理次数**（1-3）：重复处理次数，可以增强去除效果

## 使用方法

### 1. 上传图片
- 点击"选择图片"按钮
- 选择包含水印的图片文件
- 图片将在左侧预览区域显示

### 2. 调整参数
- **亮度阈值**：如果水印较亮，可以降低阈值；如果水印较暗，可以提高阈值
- **模糊半径**：增大半径可以获得更平滑的效果，但可能影响图片细节
- **处理次数**：对于顽固水印，可以增加处理次数

### 3. 处理图片
- 点击"去除水印"按钮开始处理
- 等待处理完成（通常几秒钟）
- 处理结果将在右侧显示

### 4. 下载结果
- 处理完成后，点击"下载结果"按钮
- 图片将以PNG格式保存到本地

## 技术原理

### 水印检测算法
1. **亮度分析**：计算每个像素的亮度值
2. **阈值判断**：根据设定阈值判断是否为水印像素
3. **邻域采样**：对检测到的水印像素，采样周围非水印像素
4. **像素替换**：用周围像素的平均值替换水印像素

### 处理流程
```
原始图片 → Canvas绘制 → 获取像素数据 → 水印检测 → 像素替换 → 生成结果
```

## 适用场景

### ✅ 适合处理的水印类型
- 半透明文字水印
- 简单图标水印
- 位置固定的水印
- 颜色与背景差异明显的水印

### ❌ 不适合处理的水印类型
- 复杂图案水印
- 与背景融合度高的水印
- 覆盖重要内容的水印
- 动态或随机位置的水印

## 注意事项

1. **处理效果**：本工具使用基础算法，对于复杂水印效果有限
2. **图片质量**：处理后可能会轻微影响图片质量
3. **处理时间**：大尺寸图片处理时间较长
4. **浏览器兼容性**：需要支持Canvas API的现代浏览器

## 技术栈

- **前端框架**：React 19 + TypeScript
- **UI组件**：Ant Design 5.22.5
- **图像处理**：Canvas API
- **构建工具**：Vite 7.0.4

## 开发说明

### 启动开发服务器
```bash
cd soft-gateway-admin
npm install
npm run dev
```

### 访问应用
打开浏览器访问：http://localhost:5173

### 项目结构
```
src/
├── pages/
│   └── watermark-remover/
│       └── WatermarkRemover.tsx    # 主要组件
└── main.tsx                        # 应用入口
```

## 未来改进方向

1. **算法优化**：集成更先进的图像处理算法
2. **AI增强**：使用机器学习模型进行智能水印检测
3. **批量处理**：支持多张图片批量处理
4. **格式支持**：支持更多图片格式和输出选项
5. **预设模板**：提供常见水印类型的预设参数

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。
