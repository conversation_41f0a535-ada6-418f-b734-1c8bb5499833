import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, Tag, message, Card } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined } from '@ant-design/icons';

/**
 * 测试行间指令组件
 * @component
 * @description 用于测试和展示各种工具函数的组件，包含颜色转换、设备状态、地理位置判断等功能
 * @returns {JSX.Element} 返回测试行间指令的页面组件
 */
const TestLineCommd = () => {

    /**
     * 将16进制颜色值转换为rgba格式
     * @param {string} hex - 16进制颜色值，支持3位(#RGB)或6位(#RRGGBB)格式
     * @param {number} alpha - 透明度值，范围0-1，默认为1
     * @returns {string} 返回rgba格式的颜色字符串
     * @example
     * hexToRgba("#fff", 0.5) // 返回 "rgba(255,255,255,0.5)"
     * hexToRgba("#000000", 1) // 返回 "rgba(0,0,0,1)"
     */
    const hexToRgba = (hex, alpha = 1) => {
        let r = 0, g = 0, b = 0;
        // 处理3位16进制颜色值 (#RGB)
        if (hex.length == 4) {
            r = "0x" + hex[1] + hex[1];
            g = "0x" + hex[2] + hex[2];
            b = "0x" + hex[3] + hex[3];
        // 处理6位16进制颜色值 (#RRGGBB)
        } else if (hex.length == 7) {
            r = "0x" + hex[1] + hex[2];
            g = "0x" + hex[3] + hex[4];
            b = "0x" + hex[5] + hex[6];
        }
        // 返回rgba格式的颜色字符串
        return "rgba(" + +r + "," + +g + "," + +b + "," + alpha + ")";
    };

    /**
     * 将设备状态码转换为对应的状态描述
     * @param {number} devStatus - 设备状态码
     * @returns {string} 返回设备状态的文字描述
     * @example
     * DevStatusEnum(1) // 返回 "初始化"
     * DevStatusEnum(3) // 返回 "程序加载中"
     */
    const DevStatusEnum = (devStatus) => {
        let result = ""
        switch (devStatus) {
            case 1:
                result = "初始化"
                break;
            case 3:
                result = "程序加载中"
                break;
            case 5:
                result = "硬件配置中"
                break;
        }
        return result;
    };

    /**
     * 判断经纬度坐标是否在中国境外
     * @param {number} lon - 经度值
     * @param {number} lat - 纬度值
     * @returns {boolean} 如果在中国境外返回true，否则返回false
     * @example
     * outOfChina(120.0, 30.0) // 返回 false
     * outOfChina(150.0, 60.0) // 返回 true
     */
    const outOfChina = (lon, lat) => {
        if ((lon < 72.004 || lon > 137.8347) && (lat < 0.8293 || lat > 55.8271)) {
            return true;
        } else {
            return false;
        }
    };

    /**
     * 将日期格式化为年-月-日格式，月和日补零
     * @param {Date|string|number} data - 日期对象、时间戳或日期字符串
     * @returns {string} 返回格式化后的日期字符串 (YYYY-MM-DD)
     * @example
     * ymdzeroPadding(new Date('2023-5-1')) // 返回 "2023-05-01"
     */
    const ymdzeroPadding = (data) => {
        const date = new Date(data);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要 +1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    /**
     * 根据风向编号返回对应的风向描述
     * @param {number} windDirection - 风向编号(0-7)
     * @returns {string} 返回风向描述文字
     * @example
     * dealWindDirection(0) // 返回 "北风"
     * dealWindDirection(2) // 返回 "东风"
     */
    const dealWindDirection = (windDirection) => {
        let windDirectionArr = ['北风', '东北风', '东风', '东南风', '南风', '西南风', '西风', '西北风'];
        if (windDirection || windDirection == 0) {
            return windDirectionArr[windDirection];
        } else {
            return '';
        }
    };

    /**
     * 生成UUID(通用唯一识别码)
     * @returns {string} 返回生成的UUID字符串
     * @example
     * getUUID() // 返回类似 "550e8400-e29b-41d4-a716-************" 的字符串
     */
    const getUUID = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            // eslint-disable-next-line no-bitwise
            const r = (Math.random() * 16) | 0
            // eslint-disable-next-line no-bitwise
            const v = c === 'x' ? r : (r & 0x3) | 0x8

            return v.toString(16)
        })
    };

    return (
        <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
                <h2 style={{ margin: 0 }}>测试行间指令</h2>
            </div>
        </div>
    );
};

export default TestLineCommd;